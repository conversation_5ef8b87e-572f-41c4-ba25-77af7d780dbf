import React, { useState } from "react";
import MarketingWalkinTableTopper from "./MarketingWalkinTableTopper";
import MarketingWalkinTableFilterTab from "./MarketingWalkinTableFilterTab";
import MarketingWalkinTableHeader from "./MarketingWalkinTableHeader";
import MarketingWalkinTableListingRow from "./MarketingWalkinTableListingRow";

function isSubarrayPresent(str2, str1) {
  // Check if the subArrayString is a substring of fullString
  return str2
    ?.toString()
    ?.toLowerCase()
    ?.includes(str1?.toString()?.toLowerCase());
}

const MarketingWalkinTableLayout = ({ list = [] }) => {
  const [searchState, setSearchState] = useState("");

  const update = (e) => {
    setSearchState(e);
  };

  list = list?.filter((item) => {
    return isSubarrayPresent(item?.name, searchState);
  });
  return (
    <div className="w-full h-full flex flex-col">
      <MarketingWalkinTableTopper state={searchState} setState={update} />
      <MarketingWalkinTableFilterTab />
      <div className="w-full h-full flex flex-col overflow-hidden">
        <div id="marketingwalkintable" className="w-full h-full flex flex-col overflow-auto">
          <div className="w-full h-fit flex flex-col sticky top-0 z-[1000]">
            <MarketingWalkinTableHeader />
          </div>
          <div className="w-full h-fit flex flex-col">
            <MarketingWalkinTableListingRow list={list} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketingWalkinTableLayout;
